/**
 * Enhanced Fee Report Integration Example
 * Shows how to modify existing fee reports to include component data
 */

// Original function (existing implementation)
function getReport_Original(index) {
    var cohortstudentids = cohortStudentIds[index];
    var clsId = $('#clsId').val();
    var fee_type = $('#fee_type').val();
    var installmentId = $('#installmentId').val();
    var transaction_hide_show = $("#transaction_hide_show").is(":checked") ? 1 : 0;
    
    $.ajax({
        url: 'feesv2/reports_v2/getStudentsForSummary_v2_details_new',
        type: 'post',
        data: {
            cohortstudentids: cohortstudentids,
            'clsId': clsId,
            'fee_type': fee_type,
            'installmentId': installmentId,
            'transaction_hide_show': transaction_hide_show
            // ... other parameters
        },
        success: function(data) {
            var result = JSON.parse(data);
            constructFeeTable(result, index);
        }
    });
}

// Enhanced function (new implementation)
function getReport_Enhanced(index) {
    var cohortstudentids = cohortStudentIds[index];
    var clsId = $('#clsId').val();
    var fee_type = $('#fee_type').val();
    var installmentId = $('#installmentId').val();
    var transaction_hide_show = $("#transaction_hide_show").is(":checked") ? 1 : 0;
    
    // Check if user wants component breakdown
    var include_components = $('#show_components').is(':checked') ? 'yes' : 'no';
    
    $.ajax({
        url: 'feesv2/reports_v2/getStudentsForSummary_v2_details_enhanced',
        type: 'post',
        data: {
            cohortstudentids: cohortstudentids,
            'clsId': clsId,
            'fee_type': fee_type,
            'installmentId': installmentId,
            'transaction_hide_show': transaction_hide_show,
            'include_components': include_components
            // ... other parameters
        },
        success: function(data) {
            var result = JSON.parse(data);
            
            if (result.has_component_data) {
                constructEnhancedFeeTable(result, index);
            } else {
                constructFeeTable(result, index); // Fallback to regular table
            }
        },
        error: function() {
            console.error('Error fetching enhanced fee data');
            // Fallback to original function
            getReport_Original(index);
        }
    });
}

// Enhanced table construction
function constructEnhancedFeeTable(data, index) {
    var output = '';
    var srNo = Number(index) * 150;
    
    // Build table header with component columns
    if (index === 0) {
        var headerOutput = '<thead><tr>';
        headerOutput += '<th>#</th>';
        headerOutput += '<th>Student Name</th>';
        headerOutput += '<th>Class</th>';
        headerOutput += '<th>Admission No</th>';
        headerOutput += '<th>Total Fee</th>';
        headerOutput += '<th>Total Paid</th>';
        headerOutput += '<th>Balance</th>';
        
        // Add component headers if available
        if (data.component_headers) {
            for (var componentKey in data.component_headers) {
                headerOutput += '<th colspan="3">' + componentKey + '</th>';
            }
        }
        
        headerOutput += '</tr>';
        
        // Sub-header for component columns
        if (data.component_headers) {
            headerOutput += '<tr>';
            headerOutput += '<th colspan="7"></th>'; // Empty cells for regular columns
            
            for (var componentKey in data.component_headers) {
                headerOutput += '<th>Amount</th>';
                headerOutput += '<th>Paid</th>';
                headerOutput += '<th>Balance</th>';
            }
            headerOutput += '</tr>';
        }
        
        headerOutput += '</thead>';
        $('#feeTable').html(headerOutput + '<tbody id="feeTableBody"></tbody>');
    }
    
    // Build table rows
    output += '<tbody>';
    for (var i = 0; i < data.fee_data.length; i++) {
        var student = data.fee_data[i];
        var rowIndex = i + srNo + 1;
        
        output += '<tr>';
        output += '<td>' + rowIndex + '</td>';
        output += '<td>' + (student.student_name || 'N/A') + '</td>';
        output += '<td>' + (student.class_name || 'N/A') + '</td>';
        output += '<td>' + (student.admission_no || 'N/A') + '</td>';
        output += '<td>' + formatCurrency(student.total_fee || 0) + '</td>';
        output += '<td>' + formatCurrency(student.total_fee_paid || 0) + '</td>';
        output += '<td>' + formatCurrency(student.total_balance || 0) + '</td>';
        
        // Add component data columns
        if (data.component_headers) {
            for (var componentKey in data.component_headers) {
                var componentData = null;
                
                if (student.component_breakdown && student.component_breakdown[componentKey]) {
                    componentData = student.component_breakdown[componentKey];
                }
                
                if (componentData) {
                    output += '<td>' + formatCurrency(componentData.component_amount || 0) + '</td>';
                    output += '<td>' + formatCurrency(componentData.component_amount_paid || 0) + '</td>';
                    output += '<td>' + formatCurrency(componentData.balance_amount || 0) + '</td>';
                } else {
                    output += '<td>-</td><td>-</td><td>-</td>';
                }
            }
        }
        
        output += '</tr>';
    }
    output += '</tbody>';
    
    $('#feeTableBody').append(output);
}

// Utility function to format currency
function formatCurrency(amount) {
    return '₹' + parseFloat(amount).toLocaleString('en-IN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

// Progressive enhancement approach
function initializeEnhancedReport() {
    // Add component toggle to existing UI
    var componentToggle = `
        <div class="form-group">
            <label>
                <input type="checkbox" id="show_components" value="1"> 
                Show Component Breakdown
            </label>
        </div>
    `;
    
    $('.report-filters').append(componentToggle);
    
    // Override existing report function
    window.getReport = getReport_Enhanced;
    
    // Add component-specific controls
    $('#show_components').change(function() {
        if ($(this).is(':checked')) {
            $('.component-controls').show();
        } else {
            $('.component-controls').hide();
        }
    });
}

// Backward compatibility wrapper
function getReportWithFallback(index) {
    // Try enhanced version first
    try {
        getReport_Enhanced(index);
    } catch (error) {
        console.warn('Enhanced report failed, falling back to original:', error);
        getReport_Original(index);
    }
}

// Export functions for use in existing reports
window.EnhancedFeeReport = {
    getReport: getReport_Enhanced,
    getReportOriginal: getReport_Original,
    constructEnhancedTable: constructEnhancedFeeTable,
    initialize: initializeEnhancedReport,
    formatCurrency: formatCurrency
};

// Auto-initialize if jQuery is available
$(document).ready(function() {
    if (typeof initializeEnhancedReport === 'function') {
        initializeEnhancedReport();
    }
});

/**
 * Example usage in existing report files:
 * 
 * 1. Include this script in your report view
 * 2. Replace existing getReport calls with getReport_Enhanced
 * 3. Add component toggle to your filters
 * 4. The enhanced function will automatically handle both regular and component data
 */

// Example integration in student_fees_summary.php:
/*
<script src="<?php echo base_url('assets/js/enhanced_fee_report_integration.js'); ?>"></script>
<script>
    // Override the existing getReport function
    function getReport(index) {
        return EnhancedFeeReport.getReport(index);
    }
    
    // Add component controls to existing filters
    $(document).ready(function() {
        EnhancedFeeReport.initialize();
    });
</script>
*/
