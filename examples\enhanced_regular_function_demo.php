<?php
/**
 * Demo: Enhanced Regular Function with Component Data
 * Shows how the modified getStudentsForSummary_v2_details_new now includes component data
 */

// Example of how the enhanced function now works
?>

<!DOCTYPE html>
<html>
<head>
    <title>Enhanced Regular Function Demo</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .demo-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .component-data { background-color: #f8f9fa; padding: 10px; margin: 5px 0; }
        .regular-data { background-color: #fff3cd; padding: 10px; margin: 5px 0; }
        .json-output { background-color: #f4f4f4; padding: 10px; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>

<div class="container">
    <h1>Enhanced Regular Function Demo</h1>
    
    <div class="demo-section">
        <h3>What Changed in the Regular Function</h3>
        <p><strong>Before:</strong> <code>getStudentsForSummary_v2_details_new</code> returned only regular installment data</p>
        <p><strong>After:</strong> Same function now also includes component-wise breakdown in the same response</p>
        
        <h4>New Data Structure:</h4>
        <div class="json-output">{
  "fee_data": [
    {
      "stdId": 123,
      "student_name": "John Doe",
      "total_fee": 10000,
      "total_fee_paid": 7000,
      "total_balance": 3000,
      "component_breakdown": {
        "Term 1 - Tuition Fee": {
          "component_amount": 3000,
          "component_amount_paid": 2000,
          "balance_amount": 1000
        },
        "Term 1 - Lab Fee": {
          "component_amount": 2000,
          "component_amount_paid": 1000,
          "balance_amount": 1000
        }
      }
    }
  ],
  "component_headers": {
    "Term 1 - Tuition Fee": "Term 1 - Tuition Fee",
    "Term 1 - Lab Fee": "Term 1 - Lab Fee"
  },
  "has_component_data": true
}</div>
    </div>

    <div class="demo-section">
        <h3>Test the Enhanced Function</h3>
        <button onclick="testEnhancedFunction()">Test Enhanced Regular Function</button>
        <div id="results"></div>
    </div>

    <div class="demo-section">
        <h3>How to Use in Existing Reports</h3>
        <p>The existing JavaScript code will automatically receive component data:</p>
        
        <div class="json-output">// Existing code (no changes needed):
$.ajax({
    url: 'feesv2/reports_v2/getStudentsForSummary_v2_details_new',
    data: { /* same parameters */ },
    success: function(data) {
        var result = JSON.parse(data);
        
        // Regular data (works as before)
        console.log('Fee data:', result.fee_data);
        
        // NEW: Component data (automatically included)
        if (result.has_component_data) {
            console.log('Component headers:', result.component_headers);
            
            result.fee_data.forEach(function(student) {
                if (student.component_breakdown) {
                    console.log('Components for ' + student.student_name + ':', 
                               student.component_breakdown);
                }
            });
        }
    }
});</div>
    </div>

    <div class="demo-section">
        <h3>Display Component Data in Table</h3>
        <p>The enhanced <code>constructFeeReport</code> function now shows component breakdown:</p>
        
        <table border="1" style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr>
                    <th>Student</th>
                    <th>Class</th>
                    <th>Total Fee</th>
                    <th>Paid</th>
                    <th>Balance</th>
                    <th>Component Breakdown</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>John Doe</td>
                    <td>10-A</td>
                    <td>₹10,000</td>
                    <td>₹7,000</td>
                    <td>₹3,000</td>
                    <td class="component-data">
                        <div><strong>Term 1 - Tuition Fee:</strong> Amt: ₹3,000, Paid: ₹2,000, Bal: ₹1,000</div>
                        <div><strong>Term 1 - Lab Fee:</strong> Amt: ₹2,000, Paid: ₹1,000, Bal: ₹1,000</div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="demo-section">
        <h3>Backward Compatibility</h3>
        <ul>
            <li>✅ Existing reports continue to work without changes</li>
            <li>✅ Component data is additional - doesn't break existing functionality</li>
            <li>✅ If no component data exists, <code>has_component_data</code> will be false</li>
            <li>✅ Component breakdown will be empty array if no components found</li>
        </ul>
    </div>

    <div class="demo-section">
        <h3>Performance Impact</h3>
        <ul>
            <li><strong>Minimal:</strong> Component query runs only once per batch</li>
            <li><strong>Efficient:</strong> Uses existing student IDs and fee types</li>
            <li><strong>Cached:</strong> Component headers are reused across students</li>
            <li><strong>Optional:</strong> Component data is fetched but doesn't slow down regular data</li>
        </ul>
    </div>
</div>

<script>
function testEnhancedFunction() {
    // Sample test data
    var sampleStudentIds = [1, 2, 3]; // Replace with actual student IDs
    
    $.ajax({
        url: '<?php echo site_url('feesv2/reports_v2/getStudentsForSummary_v2_details_new'); ?>',
        type: 'post',
        data: {
            'cohortstudentids': sampleStudentIds,
            'fee_type': '', // Add actual fee type
            'clsId': '', // Add actual class ID
            'transaction_hide_show': 1,
            // ... other required parameters
        },
        success: function(data) {
            var result = JSON.parse(data);
            displayTestResults(result);
        },
        error: function() {
            $('#results').html('<div style="color: red;">Error: Please ensure you have valid student IDs and parameters</div>');
        }
    });
}

function displayTestResults(data) {
    var output = '<h4>Test Results:</h4>';
    
    output += '<div class="regular-data">';
    output += '<h5>Regular Data:</h5>';
    output += '<p>Students found: ' + (data.fee_data ? data.fee_data.length : 0) + '</p>';
    output += '<p>Has component data: ' + (data.has_component_data ? 'Yes' : 'No') + '</p>';
    output += '</div>';
    
    if (data.has_component_data) {
        output += '<div class="component-data">';
        output += '<h5>Component Data:</h5>';
        output += '<p>Component types: ' + Object.keys(data.component_headers || {}).length + '</p>';
        
        if (data.component_headers) {
            output += '<ul>';
            for (var header in data.component_headers) {
                output += '<li>' + header + '</li>';
            }
            output += '</ul>';
        }
        
        output += '</div>';
    }
    
    // Show sample student data
    if (data.fee_data && data.fee_data.length > 0) {
        var student = data.fee_data[0];
        output += '<div class="json-output">';
        output += '<h5>Sample Student Data:</h5>';
        output += JSON.stringify({
            student_name: student.student_name,
            total_fee: student.total_fee,
            component_breakdown: student.component_breakdown
        }, null, 2);
        output += '</div>';
    }
    
    $('#results').html(output);
}

// Show how to check for component data in existing code
function handleExistingReportData(data) {
    // This is how existing reports can be enhanced
    var result = JSON.parse(data);
    
    // Regular processing (unchanged)
    var fee_data = result.fee_data;
    var headers = result.headers;
    
    // NEW: Component processing (optional)
    if (result.has_component_data) {
        var component_headers = result.component_headers;
        
        // Process each student's component data
        fee_data.forEach(function(student) {
            if (student.component_breakdown) {
                console.log('Student ' + student.student_name + ' has component data');
                
                // Display component breakdown
                for (var componentKey in student.component_breakdown) {
                    var component = student.component_breakdown[componentKey];
                    console.log(componentKey + ': Amount=' + component.component_amount + 
                               ', Paid=' + component.component_amount_paid);
                }
            }
        });
    }
}
</script>

</body>
</html>
